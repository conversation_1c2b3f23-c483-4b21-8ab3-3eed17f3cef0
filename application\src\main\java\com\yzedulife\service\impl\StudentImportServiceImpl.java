package com.yzedulife.service.impl;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.StudentImportService;
import com.yzedulife.service.dto.StudentImportDTO;
import com.yzedulife.service.dto.StudentSchoolDTO;
import com.yzedulife.service.dto.StudentClassDTO;
import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.service.service.StudentSchoolService;
import com.yzedulife.service.service.StudentClassService;
import com.yzedulife.service.service.StudentUserService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生导入服务实现类（应用层业务服务）
 */
@Service
public class StudentImportServiceImpl implements StudentImportService {

    @Autowired
    private StudentSchoolService studentSchoolService;

    @Autowired
    private StudentClassService studentClassService;

    @Autowired
    private StudentUserService studentUserService;

    @Override
    public List<StudentImportDTO> importStudentsFromExcel(InputStream inputStream, String fileName) throws BusinessException {
        List<StudentImportDTO> importResults = new ArrayList<>();
        
        try {
            Workbook workbook = createWorkbook(inputStream, fileName);
            Sheet sheet = workbook.getSheetAt(0);
            
            // 解析表头，获取字段位置映射
            Map<String, Integer> fieldIndexMap = parseHeader(sheet);
            
            // 从第二行开始处理数据
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) continue;
                
                StudentImportDTO importDTO = parseRowData(row, fieldIndexMap, rowIndex + 1);
                if (importDTO != null) {
                    importResults.add(importDTO);
                }
            }
            
            workbook.close();
            
        } catch (Exception e) {
            throw new BusinessException("Excel文件解析失败: " + e.getMessage());
        }
        
        return importResults;
    }

    @Override
    public StudentImportService.ImportResult processImportData(List<StudentImportDTO> importDataList) throws BusinessException {
        List<StudentImportDTO> successResults = new ArrayList<>();
        List<StudentImportDTO> failureResults = new ArrayList<>();
        
        // 用于缓存已创建的学校和班级，避免重复查询
        Map<String, Long> schoolCache = new HashMap<>();
        Map<String, Long> classCache = new HashMap<>();
        
        for (StudentImportDTO importData : importDataList) {
            try {
                processOneRecord(importData, schoolCache, classCache);
                // 成功处理的记录
                successResults.add(importData);
                
            } catch (Exception e) {
                // 处理失败的记录，记录错误信息
                importData.setErrorMessage(e.getMessage());
                failureResults.add(importData);
            }
        }
        
        return new StudentImportService.ImportResult(successResults, failureResults);
    }

    /**
     * 处理单条记录（独立事务）
     */
    private void processOneRecord(StudentImportDTO importData, Map<String, Long> schoolCache, Map<String, Long> classCache) throws BusinessException {
        // 验证必填字段
        validateImportData(importData);
        
        // 1. 处理学校
        Long schoolId = getOrCreateSchool(importData.getSchoolName(), schoolCache);
        
        // 2. 处理班级
        String classKey = schoolId + "_" + importData.getClassName();
        Long classId = getOrCreateClass(importData.getClassName(), schoolId, classKey, classCache);
        
        // 3. 检查学号是否已存在
        if (studentUserService.isStudentNumberExistInClass(importData.getStudentNumber(), classId)) {
            // 学号已存在，跳过但不算失败
            return;
        }
        
        // 4. 创建学生
        StudentUserDTO studentUserDTO = new StudentUserDTO();
        studentUserDTO.setName(importData.getName());
        studentUserDTO.setStudentNumber(importData.getStudentNumber());
        studentUserDTO.setClassId(classId);
        
        studentUserService.create(studentUserDTO);
    }

    /**
     * 验证导入数据
     */
    private void validateImportData(StudentImportDTO importData) throws BusinessException {
        if (!StringUtils.hasText(importData.getSchoolName())) {
            throw new BusinessException("学校名称不能为空");
        }
        if (!StringUtils.hasText(importData.getClassName())) {
            throw new BusinessException("班级名称不能为空");
        }
        if (!StringUtils.hasText(importData.getStudentNumber())) {
            throw new BusinessException("学号不能为空");
        }
        if (!StringUtils.hasText(importData.getName())) {
            throw new BusinessException("姓名不能为空");
        }
    }

    /**
     * 获取或创建学校
     */
    private Long getOrCreateSchool(String schoolName, Map<String, Long> schoolCache) throws BusinessException {
        if (schoolCache.containsKey(schoolName)) {
            return schoolCache.get(schoolName);
        }
        
        try {
            // 尝试获取已存在的学校
            StudentSchoolDTO existingSchool = studentSchoolService.getBySchoolName(schoolName);
            schoolCache.put(schoolName, existingSchool.getId());
            return existingSchool.getId();
        } catch (BusinessException e) {
            // 学校不存在，创建新学校
            StudentSchoolDTO newSchool = new StudentSchoolDTO();
            newSchool.setSchoolName(schoolName);
            StudentSchoolDTO createdSchool = studentSchoolService.create(newSchool);
            schoolCache.put(schoolName, createdSchool.getId());
            return createdSchool.getId();
        }
    }

    /**
     * 获取或创建班级
     */
    private Long getOrCreateClass(String className, Long schoolId, String classKey, Map<String, Long> classCache) throws BusinessException {
        if (classCache.containsKey(classKey)) {
            return classCache.get(classKey);
        }
        
        try {
            // 尝试获取已存在的班级
            StudentClassDTO existingClass = studentClassService.getBySchoolIdAndClassName(schoolId, className);
            classCache.put(classKey, existingClass.getId());
            return existingClass.getId();
        } catch (BusinessException e) {
            // 班级不存在，创建新班级
            StudentClassDTO newClass = new StudentClassDTO();
            newClass.setClassName(className);
            newClass.setSchoolId(schoolId);
            StudentClassDTO createdClass = studentClassService.create(newClass);
            classCache.put(classKey, createdClass.getId());
            return createdClass.getId();
        }
    }

    /**
     * 根据文件名创建对应的Workbook
     */
    private Workbook createWorkbook(InputStream inputStream, String fileName) throws Exception {
        if (fileName.endsWith(".xlsx")) {
            return new XSSFWorkbook(inputStream);
        } else if (fileName.endsWith(".xls")) {
            return new HSSFWorkbook(inputStream);
        } else {
            throw new BusinessException("不支持的文件格式，请使用.xls或.xlsx文件");
        }
    }

    /**
     * 解析表头，获取字段位置映射
     */
    private Map<String, Integer> parseHeader(Sheet sheet) throws BusinessException {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new BusinessException("Excel文件表头不能为空");
        }
        
        Map<String, Integer> fieldIndexMap = new HashMap<>();
        
        for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
            Cell cell = headerRow.getCell(cellIndex);
            if (cell != null) {
                String headerValue = getCellStringValue(cell).trim();
                fieldIndexMap.put(headerValue, cellIndex);
            }
        }
        
        // 验证必需的字段是否存在
        String[] requiredFields = {"学校", "班级", "学号", "姓名"};
        for (String field : requiredFields) {
            if (!fieldIndexMap.containsKey(field)) {
                throw new BusinessException("Excel文件缺少必需的字段: " + field);
            }
        }
        
        return fieldIndexMap;
    }

    /**
     * 解析行数据
     */
    private StudentImportDTO parseRowData(Row row, Map<String, Integer> fieldIndexMap, int rowNumber) {
        try {
            String schoolName = getCellStringValue(row.getCell(fieldIndexMap.get("学校")));
            String className = getCellStringValue(row.getCell(fieldIndexMap.get("班级")));
            String studentNumber = getCellStringValue(row.getCell(fieldIndexMap.get("学号")));
            String name = getCellStringValue(row.getCell(fieldIndexMap.get("姓名")));
            
            // 跳过空行
            if (!StringUtils.hasText(schoolName) && !StringUtils.hasText(className) && 
                !StringUtils.hasText(studentNumber) && !StringUtils.hasText(name)) {
                return null;
            }
            
            StudentImportDTO importDTO = new StudentImportDTO();
            importDTO.setRowNumber(rowNumber);
            importDTO.setSchoolName(schoolName);
            importDTO.setClassName(className);
            importDTO.setStudentNumber(studentNumber);
            importDTO.setName(name);
            
            return importDTO;
            
        } catch (Exception e) {
            // 如果解析失败，返回一个包含错误信息的DTO
            StudentImportDTO errorDTO = new StudentImportDTO();
            errorDTO.setRowNumber(rowNumber);
            return errorDTO;
        }
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}
