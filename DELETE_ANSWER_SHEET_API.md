# 删除答卷接口文档

## 接口概述

新增了删除答卷的API接口，该接口会同时删除答卷及其关联的所有答案详情。

## 接口详情

### 删除答卷

**接口地址：** `POST /answer/delete`

**权限要求：** 管理员权限 (`admin`)

**请求参数：**
- `id` (Long, 必填): 答卷ID

**请求示例：**
```http
POST /answer/delete?id=123
Authorization: Bearer <admin_token>
```

**响应格式：**

成功响应：
```json
{
    "success": true,
    "msg": "删除答卷成功",
    "data": null
}
```

失败响应：
```json
{
    "success": false,
    "msg": "错误信息",
    "data": null
}
```

**可能的错误信息：**
- "答卷ID不能为空" - 当id参数为空时
- "答卷不存在" - 当指定的答卷不存在时
- "删除答卷失败" - 当数据库删除操作失败时

## 实现细节

### 服务层实现 (AnswerSheetServiceImpl.deleteById)

1. **参数验证**：检查答卷ID是否为空
2. **存在性验证**：检查答卷是否存在
3. **级联删除**：先删除所有关联的答案详情 (AnswerDetail)
4. **删除答卷**：最后删除答卷本身

### 控制器层实现 (AnswerController.delete)

1. **权限控制**：使用 `@Token("admin")` 注解限制只有管理员可以访问
2. **异常处理**：捕获业务异常和系统异常，返回相应的错误信息
3. **日志记录**：记录删除失败的详细错误信息

## 数据库操作

删除操作按以下顺序执行：

1. 删除 `answer_details` 表中所有 `answer_sheet_id` 等于指定ID的记录
2. 删除 `answer_sheets` 表中指定ID的记录

这确保了数据的完整性，避免了外键约束错误。

## 测试用例

已添加以下测试用例：

1. **delete_Success** - 成功删除答卷
2. **delete_Failed** - 删除操作返回false的情况
3. **delete_BusinessException** - 业务异常处理（如答卷不存在）
4. **delete_NoPermission** - 权限验证（非管理员用户访问）

## 使用注意事项

1. **权限要求**：只有管理员用户可以删除答卷
2. **级联删除**：删除答卷会同时删除所有关联的答案详情，此操作不可逆
3. **事务性**：删除操作在事务中执行，确保数据一致性
4. **日志记录**：所有删除操作都会记录日志，便于审计和问题排查

## API集成

该接口遵循项目现有的API设计模式：
- 使用统一的Response格式
- 遵循RESTful设计原则
- 包含完整的错误处理
- 支持Swagger文档生成
