package com.yzedulife.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.service.service.*;
import com.yzedulife.vo.AnswerDetailVO;
import com.yzedulife.vo.AnswerSubmitVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AnswerController.class)
@ActiveProfiles("test")
@DisplayName("答案控制器参数测试")
class AnswerControllerParameterTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AnswerSheetService answerSheetService;

    @MockBean
    private AnswerDetailService answerDetailService;

    @MockBean
    private QuestionOptionService questionOptionService;

    @MockBean
    private StudentUserService studentUserService;

    @MockBean
    private StudentClassService studentClassService;

    @MockBean
    private OtherUserService otherUserService;

    @MockBean
    private QuestionnaireService questionnaireService;

    @Test
    @DisplayName("提交答案 - questionnaireId参数为NaN")
    void submit_QuestionnaireIdIsNaN() throws Exception {
        // Given
        List<AnswerDetailVO> answerDetails = new ArrayList<>();
        AnswerDetailVO answerDetail = new AnswerDetailVO();
        answerDetail.setQuestionId(1L);
        answerDetail.setChosenOptionCode("A");
        answerDetails.add(answerDetail);

        // When & Then
        mockMvc.perform(post("/answer/submit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .param("questionnaireId", "NaN") // 传入NaN
                        .content(objectMapper.writeValueAsString(answerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(100003))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("参数 'questionnaireId' 的值 'NaN' 无效")));
    }

    @Test
    @DisplayName("提交答案 - questionnaireId参数为空字符串")
    void submit_QuestionnaireIdIsEmpty() throws Exception {
        // Given
        List<AnswerDetailVO> answerDetails = new ArrayList<>();
        AnswerDetailVO answerDetail = new AnswerDetailVO();
        answerDetail.setQuestionId(1L);
        answerDetail.setChosenOptionCode("A");
        answerDetails.add(answerDetail);

        // When & Then
        mockMvc.perform(post("/answer/submit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .param("questionnaireId", "") // 传入空字符串
                        .content(objectMapper.writeValueAsString(answerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(100003));
    }

    @Test
    @DisplayName("提交答案 - questionnaireId参数为非数字字符串")
    void submit_QuestionnaireIdIsInvalidString() throws Exception {
        // Given
        List<AnswerSubmitVO> answerDetails = new ArrayList<>();
        AnswerSubmitVO answerDetail = new AnswerSubmitVO();
        answerDetail.setQuestionId(1L);
        answerDetail.setChosenOptionCode("A");
        answerDetails.add(answerDetail);

        // When & Then
        mockMvc.perform(post("/answer/submit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .param("questionnaireId", "abc123") // 传入非数字字符串
                        .content(objectMapper.writeValueAsString(answerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(100003))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("参数 'questionnaireId' 的值 'abc123' 无效")));
    }
}
